{% extends "base.html" %}

{% block title %}Inscription - Portail PDF Étudiant{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-left">
        <div class="auth-form-card">
            <h1 class="auth-title"><PERSON><PERSON><PERSON> un compte</h1>
            <p class="auth-subtitle">Rejoignez EduShare pour commencer à partager vos documents</p>

            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="alert alert-danger">
                        {% for message in messages %}
                            <p>{{ message }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            <form method="POST" autocomplete="off">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="first_name">Prénom *</label>
                        <input type="text" id="first_name" name="first_name" placeholder="Votre prénom" required autocomplete="off" maxlength="50" pattern="[A-Za-zÀ-ÿ\s\-']{1,50}">
                    </div>

                    <div class="form-group">
                        <label for="last_name">Nom *</label>
                        <input type="text" id="last_name" name="last_name" placeholder="Votre nom" required autocomplete="off" maxlength="50" pattern="[A-Za-zÀ-ÿ\s\-']{1,50}">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Adresse email *</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required autocomplete="off" maxlength="100">
                </div>

                <div class="form-group">
                    <label for="phone">Téléphone</label>
                    <input type="tel" id="phone" name="phone" placeholder="Votre numéro de téléphone" autocomplete="off" maxlength="20" pattern="[0-9\+\-\s\(\)]{0,20}">
                </div>

                <div class="form-group">
                    <label for="date_of_birth">Date de naissance</label>
                    <input type="date" id="date_of_birth" name="date_of_birth" autocomplete="off" min="1900-01-01" max="{{ today }}">
                </div>

                <div class="form-group">
                    <label for="password">Mot de passe * (min. 8 caractères)</label>
                    <input type="password" id="password" name="password" placeholder="Créez un mot de passe sécurisé" required autocomplete="new-password" maxlength="200" minlength="8">
                    <small class="form-text text-muted">Le mot de passe doit contenir au moins 8 caractères.</small>
                </div>

                <button type="submit" class="btn btn-primary">Créer mon compte</button>
            </form>

            <div style="text-align: center; margin: 0.6rem 0;">
                <a href="{{ url_for('google_login') }}" class="btn btn-google">
                    <span class="google-icon"></span>
                    Continuer avec Google
                </a>
            </div>

            <div class="signup-link">
                Vous avez déjà un compte? <a href="{{ url_for('login') }}">Connectez-vous</a>
            </div>
        </div>
    </div>

    <div class="auth-right">
        <div class="illustration">
            <img src="{{ url_for('static', filename='sideimage.png') }}" alt="EduShare" class="illustration-image">
            <div class="illustration-content">
                <h3>Rejoignez EduShare</h3>
                <p>Créez votre compte et commencez à partager vos documents éducatifs en toute sécurité</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

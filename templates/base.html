<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <title>{% block title %}Portail PDF Étudiant{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Default container for non-auth pages */
        body:not(.auth-page) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-container {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
        }

        .auth-left {
            flex: 1.2;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            max-width: 650px;
        }

        .auth-form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            margin: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .auth-right {
            flex: 1;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .home-container {
            max-width: 600px;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
                margin: 0;
            }

            .auth-right {
                min-height: 200px;
                order: -1;
            }

            .auth-left {
                padding: 1rem;
            }

            .auth-form-card {
                margin: 0.5rem;
                padding: 1.5rem;
            }

            .illustration-image {
                width: 150px;
            }
        }
        
        h1, h2 {
            color: #333;
            margin-bottom: 2rem;
            font-size: 2rem;
            font-weight: 600;
        }

        .auth-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #1a1a1a;
        }

        .auth-subtitle {
            color: #666;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .illustration {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .illustration-image {
            width: 250px;
            height: auto;
            object-fit: contain;
            margin-bottom: 1.5rem;
        }

        .illustration-content {
            text-align: center;
            color: white;
        }

        .illustration h3 {
            font-size: 1.3rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .illustration p {
            font-size: 0.85rem;
            opacity: 0.95;
            max-width: 250px;
            line-height: 1.4;
            color: white;
        }


        
        .form-group {
            margin-bottom: 1.2rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
            font-size: 1rem;
        }

        input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="date"], select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, input[type="tel"]:focus, input[type="date"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.5rem 0;
            font-size: 0.75rem;
        }

        .remember-forgot a {
            color: #667eea;
            text-decoration: none;
        }

        .remember-forgot a:hover {
            text-decoration: underline;
        }

        .signup-link {
            text-align: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 0.8rem;
        }

        .signup-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }
        
        .btn-google {
            background-color: white;
            color: #757575;
            border: 1px solid #dadce0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-google:hover {
            background-color: #f8f9fa;
            border-color: #dadce0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .google-icon {
            width: 18px;
            height: 18px;
            background-image: url("{{ url_for('static', filename='googleicon.svg') }}");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            flex-shrink: 0;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .divider {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #666;
        }
        
        .link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .link a {
            color: #667eea;
            text-decoration: none;
        }
        
        .link a:hover {
            text-decoration: underline;
        }
        
        .flash-messages {
            margin-bottom: 1rem;
        }
        
        .flash {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        
        .flash.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .flash.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .flash.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .welcome-card {
            text-align: center;
            padding: 2rem;
        }
        
        .user-name {
            font-size: 1.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .welcome-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body{% if request.endpoint in ['login', 'signup'] %} class="auth-page"{% endif %}>
    {% if request.endpoint not in ['login', 'signup'] %}
    <div class="container {% if request.endpoint == 'home' %}home-container{% endif %}">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash {{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
    {% endif %}

    {% block content %}{% endblock %}

    {% if request.endpoint not in ['login', 'signup'] %}
    </div>
    {% endif %}
</body>
</html>

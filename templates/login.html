{% extends "base.html" %}

{% block title %}Connexion - Portail PDF Étudiant{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-left">
        <div class="auth-form-card">
            <h1 class="auth-title">Bon retour</h1>
            <p class="auth-subtitle">Connectez-vous à votre compte pour continuer</p>

            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="alert alert-danger">
                        {% for message in messages %}
                            <p>{{ message }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            <form method="POST" autocomplete="on">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="form-group">
                    <label for="email">Adresse email</label>
                    <input type="email" id="email" name="email" placeholder="Entrez votre email" required autocomplete="username" maxlength="100">
                </div>

                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" placeholder="Entrez votre mot de passe" required autocomplete="current-password" maxlength="200" minlength="8">
                </div>

                <div class="remember-forgot">
                    <label style="display: flex; align-items: center; font-size: 0.9rem;">
                        <input type="checkbox" style="margin-right: 0.5rem; width: auto;">
                        Se souvenir de moi
                    </label>
                    <a href="#">Mot de passe oublié?</a>
                </div>

                <button type="submit" class="btn btn-primary">Se connecter</button>
            </form>

            <div style="text-align: center; margin: 0.6rem 0;">
                <a href="{{ url_for('google_login') }}" class="btn btn-google">
                    <span class="google-icon"></span>
                    Continuer avec Google
                </a>
            </div>

            <div class="signup-link">
                Vous n'avez pas de compte? <a href="{{ url_for('signup') }}">Inscrivez-vous</a>
            </div>
        </div>
    </div>

    <div class="auth-right">
        <div class="illustration">
            <img src="{{ url_for('static', filename='sideimage.png') }}" alt="EduShare" class="illustration-image">
            <div class="illustration-content">
                <h3>Bienvenue sur EduShare</h3>
                <p>Votre plateforme sécurisée pour partager et gérer vos documents éducatifs</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

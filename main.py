import os
import secrets
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSR<PERSON>rotect
# from flask_talisman import Talisman  # Temporarily disabled
from werkzeug.security import generate_password_hash, check_password_hash
from authlib.integrations.flask_client import OAuth
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Security Configuration
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY')
if not app.config['SECRET_KEY'] or app.config['SECRET_KEY'] == 'super-secret-key-change-this-in-production-12345':
    # Generate a secure secret key if not provided or using default
    app.config['SECRET_KEY'] = secrets.token_hex(32)
    print("WARNING: Using generated secret key. Set FLASK_SECRET_KEY in production!")

# Database Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///app.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Session Security Configuration
app.config['SESSION_COOKIE_SECURE'] = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)  # Session expires after 2 hours

# CSRF Protection
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # 1 hour

# Initialize extensions
db = SQLAlchemy(app)
csrf = CSRFProtect(app)

# Security Headers (Manual implementation)
# Note: Install flask-talisman for production use
# talisman = Talisman(app, ...)  # Temporarily disabled

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
login_manager.session_protection = "strong"  # Enhanced session protection

# OAuth setup
oauth = OAuth(app)
google = oauth.register(
    "myApp",
    client_id=os.getenv('GOOGLE_CLIENT_ID'),
    client_secret=os.getenv('GOOGLE_CLIENT_SECRET'),
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_kwargs={'scope': 'openid email profile'},
)

# User Model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(100), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    password_hash = db.Column(db.String(200), nullable=True)  # Nullable for Google users
    is_google_user = db.Column(db.Boolean, default=False)

    # Additional student details (optional)
    phone = db.Column(db.String(20), nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)

    @property
    def name(self):
        return f"{self.first_name} {self.last_name}"
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Security middleware
@app.before_request
def security_headers():
    """Add security headers to all responses"""
    # Prevent access to sensitive routes without authentication
    protected_routes = ['home', 'logout']
    if request.endpoint in protected_routes and not current_user.is_authenticated:
        flash('Veuillez vous connecter pour accéder à cette page.', 'warning')
        return redirect(url_for('login'))

    # Make sessions permanent and refresh them
    if current_user.is_authenticated:
        session.permanent = True

@app.after_request
def after_request(response):
    """Add comprehensive security headers"""
    # Basic security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # Content Security Policy
    csp = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://accounts.google.com https://apis.google.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self' https://accounts.google.com; "
        "frame-src https://accounts.google.com; "
        "object-src 'none'; "
        "base-uri 'self'"
    )
    response.headers['Content-Security-Policy'] = csp

    # HSTS for production
    if os.getenv('FLASK_ENV') == 'production':
        response.headers['Strict-Transport-Security'] = 'max-age=********; includeSubDomains'

    return response

# Routes
@app.route('/')
def index():
    """Landing page - redirects based on authentication status"""
    if current_user.is_authenticated:
        return redirect(url_for('home'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Secure login route with rate limiting and input validation"""
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        # Basic rate limiting - track failed attempts
        session_key = f"login_attempts_{request.remote_addr}"
        attempts = session.get(session_key, 0)

        if attempts >= 5:
            flash('Trop de tentatives de connexion. Veuillez réessayer dans quelques minutes.', 'error')
            return render_template('login.html'), 429

        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')

        # Input validation
        if not email or not password:
            flash('Veuillez remplir tous les champs.', 'error')
            return render_template('login.html')

        if len(email) > 100 or len(password) > 200:
            flash('Données invalides.', 'error')
            return render_template('login.html')

        user = User.query.filter_by(email=email).first()

        if user and user.check_password(password):
            # Reset failed attempts on successful login
            session.pop(session_key, None)
            login_user(user, remember=False)  # Don't remember by default for security

            # Redirect to intended page or home
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            return redirect(url_for('home'))
        else:
            # Increment failed attempts
            session[session_key] = attempts + 1
            flash('Email ou mot de passe invalide.', 'error')

    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    """Secure signup route with input validation and sanitization"""
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        # Get and sanitize form data
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        phone = request.form.get('phone', '').strip()
        date_of_birth_str = request.form.get('date_of_birth', '').strip()

        # Input validation
        if not first_name or not last_name or not email or not password:
            flash('Veuillez remplir tous les champs obligatoires.', 'error')
            return render_template('signup.html')

        # Length validation
        if (len(first_name) > 50 or len(last_name) > 50 or
            len(email) > 100 or len(password) > 200 or len(phone) > 20):
            flash('Un ou plusieurs champs sont trop longs.', 'error')
            return render_template('signup.html')

        # Password strength validation
        if len(password) < 8:
            flash('Le mot de passe doit contenir au moins 8 caractères.', 'error')
            return render_template('signup.html')

        # Email format validation (basic)
        if '@' not in email or '.' not in email.split('@')[-1]:
            flash('Format d\'email invalide.', 'error')
            return render_template('signup.html')

        # Check if email already exists
        if User.query.filter_by(email=email).first():
            flash('Cet email existe déjà. Veuillez utiliser un autre email.', 'error')
            return render_template('signup.html')

        # Parse date of birth if provided
        date_of_birth = None
        if date_of_birth_str:
            try:
                date_of_birth = datetime.strptime(date_of_birth_str, '%Y-%m-%d').date()
                # Validate reasonable date range
                if date_of_birth.year < 1900 or date_of_birth > datetime.now().date():
                    flash('Date de naissance invalide.', 'error')
                    return render_template('signup.html')
            except ValueError:
                flash('Format de date invalide.', 'error')
                return render_template('signup.html')

        try:
            user = User(
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=phone if phone else None,
                date_of_birth=date_of_birth
            )
            user.set_password(password)
            db.session.add(user)
            db.session.commit()

            login_user(user, remember=False)
            flash('Compte créé avec succès!', 'success')
            return redirect(url_for('home'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création du compte. Veuillez réessayer.', 'error')
            return render_template('signup.html', today=datetime.now().date())

    return render_template('signup.html', today=datetime.now().date())

@app.route('/auth/google')
def google_login():
    redirect_uri = url_for('google_callback', _external=True)
    return google.authorize_redirect(redirect_uri)

@app.route('/auth/google/callback')
def google_callback():
    token = google.authorize_access_token()
    user_info = token.get('userinfo')

    if user_info:
        email = user_info['email']
        name = user_info['name']

        # Split name into first and last name
        name_parts = name.split(' ', 1)
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ''

        user = User.query.filter_by(email=email).first()

        if not user:
            user = User(
                first_name=first_name,
                last_name=last_name,
                email=email,
                is_google_user=True
            )
            db.session.add(user)
            db.session.commit()

        login_user(user)
        return redirect(url_for('home'))

    flash('Échec de l\'authentification Google.', 'error')
    return redirect(url_for('login'))

@app.route('/home')
@login_required
def home():
    return render_template('home.html', user=current_user)

@app.route('/logout', methods=['POST'])  # Only allow POST for logout
@login_required
def logout():
    """Secure logout with session cleanup"""
    # Clear all session data
    session.clear()
    logout_user()
    flash('Vous avez été déconnecté avec succès.', 'info')
    return redirect(url_for('login'))

# Add a GET route for logout that shows a confirmation form
@app.route('/logout-confirm')
@login_required
def logout_confirm():
    """Show logout confirmation page"""
    return render_template('logout_confirm.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
